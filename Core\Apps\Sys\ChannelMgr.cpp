#include <string.h>
#include "Predef.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysLog.h"
#include "Timer.h"
#include "RxModem.h"
#include "VdlRxMgr.h"
#include "VdlTxMgr.h"
#include "LayerNetwork.h"
#include "LayerPhysical.h"
#include "TestModeMgr.h"
#include "AisModem.h"
#include "AlarmThing.h"
#include "SetupMgr.h"
#include "SysOpStatus.h"
#include "ChannelMgr.h"

CChannelMgr::CChannelMgr(UINT16 uChNum, UINT16 nLongRangeChID, int nHwLocalRxID)
    : CFrameMapMgr(this), CChTxScheduler(this)
{
    m_pVdlRxMgr     = new CVdlRxMgr(this);
    m_pVdlTxMgr     = new CVdlTxMgr(this);

    if (uChNum == AIS1_DEFAULT_CH_NUM)  m_pRxModem = CRxModem::getInstRxA();
    else                                m_pRxModem = CRxModem::getInstRxB();

    m_nHwLocalRxID  = nHwLocalRxID;

    m_nChNumLR      = nLongRangeChID;
    m_nChNumTx      = 0;
    m_nChNumRx      = 0;
    m_uInitChNum    = uChNum;

    Initialize();

    //SetTxChannelNumber(uChNum);
    //SetRxChannelNumber(uChNum);

    //m_bTxEnableMode = true;

    InitChannel();
}

CChannelMgr::~CChannelMgr()
{
}

/**
 * @brief Initialize the channel operation
 */
void CChannelMgr::Initialize(void)
{
    SetChOpPhase(OPPHASE_MONITOR_VDL);
}

/**
 * @brief Initialize the channel
 */
void CChannelMgr::InitChannel(void)
{
    SetTxChannelNumber(m_uInitChNum);
    SetRxChannelNumber(m_uInitChNum);

    ClearFrameMap();
    m_pVdlTxMgr->InitAllTxBuff();

    SetTxEnableModeCh(true);
    SetRxEnableModeCh(true);
}

/**
 * @brief Reload setup
 */
void CChannelMgr::ReloadSetup(void)
{
    if(GetChOrdinal() == AIS_CHANNEL_AIS1) {
        m_nChNumLR = CSetupMgr::getInst()->GetLongRangeCh1();
    }
    else {
        m_nChNumLR = CSetupMgr::getInst()->GetLongRangeCh2();
    }
}

/**
 * @brief Get channel ordinal
 * @return Channel ordinal
 */
UINT8 CChannelMgr::GetChOrdinal(void)
{
    if(this == CLayerNetwork::getInst()->GetChPrimary())
        return AIS_CHANNEL_AIS1;
    return AIS_CHANNEL_AIS2;
}

/**
 * @brief Set Rx enable mode
 * @param bRxEnable true to enable, false to disable
 */
void CChannelMgr::SetRxEnableModeCh(bool bRxEnable)
{
    m_bRxEnableMode = bRxEnable;
}

/**
 * @brief Check if Rx is available
 * @return true if available, false otherwise
 */
bool CChannelMgr::IsRxAvailableCh(void)
{
    //------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 6.10.2.2 Table 2
    // when Alarm 003, 004 occurred -> "Stop transmission on affected channel"
    //------------------------------------------------------------------------------------------
    CAlarmThing *pAlarmRxMalFunc = gpAlarmRxMalfuncCh1;
    if(this == CLayerNetwork::getInst()->GetChSecondary())
        pAlarmRxMalFunc = gpAlarmRxMalfuncCh2;

    return (CTestModeMgr::getInst()->IsTestModeRunning() || (m_bRxEnableMode && !pAlarmRxMalFunc->IsAlarmOccurred()));
}

/**
 * @brief Set Tx enable mode
 * @param bTxEnable true to enable, false to disable
 */
void CChannelMgr::SetTxEnableModeCh(bool bTxEnable)
{
    m_bTxEnableMode = bTxEnable;
}

/**
 * @brief Check if Tx is available
 * @return true if available, false otherwise
 */
bool CChannelMgr::IsTxAvailableCh(void)
{
    //------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 6.10.2.2 Table 2
    // when Alarm 003, 004 occurred -> "Stop transmission on affected channel"
    //------------------------------------------------------------------------------------------

    return m_bTxEnableMode;
}

/**
 * @brief Check if Tx channel is healthy
 * @return true if healthy, false otherwise
 */
bool CChannelMgr::IsTxChHealthy(void)
{
    //------------------------------------------------------------------------------------------
    // Refer to IEC-61993-2 6.10.2.2 Table 2
    // when Alarm 003, 004 occurred -> "Stop transmission on affected channel"
    //------------------------------------------------------------------------------------------
    CAlarmThing *pAlarmRxMalFunc = gpAlarmRxMalfuncCh1;
    if(this == CLayerNetwork::getInst()->GetChSecondary())
        pAlarmRxMalFunc = gpAlarmRxMalfuncCh2;

    return !pAlarmRxMalFunc->IsAlarmOccurred();
}

/**
 * @brief Set channel operation mode
 * @param bOpMode Operation mode to set
 */
void CChannelMgr::SetChOpMode(BYTE bOpMode)
{
    m_nChOpMode = bOpMode;

    m_nRoutinePosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
}

/**
 * @brief Get channel operation mode
 * @return Channel operation mode
 */
INT8 CChannelMgr::GetChOpMode(void)
{
    return m_nChOpMode;
}

/**
 * @brief Set channel operation phase
 * @param bOpPhase Operation phase to set
 */
void CChannelMgr::SetChOpPhase(BYTE bOpPhase)
{
    m_nChOpPhase = bOpPhase;
    m_dwChOpPhaseStartTick = SysGetSystemTimer();

    if(bOpPhase == OPPHASE_ROUTINE)
    {
        m_dwRoutineAllocLastCheckSec= cTimerSys::getInst()->GetCurTimerSec()+m_fChReportIntervalSec;
        m_wRoutineAllocLastCheckSlot= OPSTATUS::nCurFrameMapSlotID;
        m_nRoutinePosMsgID = CAisLib::GetScheduledPosReportMsgID(CLayerNetwork::getInst()->m_nOpMode);
        m_nUrgencyTxSlotID = SLOTID_NONE;
    }
}

/**
 * @brief Get channel operation phase
 * @return Channel operation phase
 */
INT8 CChannelMgr::GetChOpPhase(void)
{
    return m_nChOpPhase;
}

/**
 * @brief Set channel number for reception
 * @param uChNum Channel number to set
 * @return true if successful, false otherwise
 */
bool CChannelMgr::SetRxChannelNumber(UINT16 uChNum)
{
    bool bRet = true;
    if(uChNum != m_nChNumRx)
    {
        DEBUG_LOG("SetRxChannelNumber] CH : %d, Freq : %d\r\n", uChNum, CAisLib::GetAisFreqByChannelNo(uChNum));

        if((bRet = CLayerPhysical::getInst()->SetRxLocalFreq(m_nHwLocalRxID, uChNum)))
        {
            m_nChNumRx = uChNum;
        }
        else
        {
            WARNING_LOG("SetRxChannelNumber] Error, CH : %d, Freq : %d\r\n", uChNum, CAisLib::GetAisFreqByChannelNo(uChNum));
        }
    }
    return bRet;
}

/**
 * @brief Get channel number for reception
 * @return Channel number for reception
 */
UINT16 CChannelMgr::GetRxChannelNumber(void)
{
    return m_nChNumRx;
}

/**
 * @brief Set channel number for transmission
 * @param uChNum Channel number to set
 * @return true if successful, false otherwise
 */
bool CChannelMgr::SetTxChannelNumber(UINT16 uChNum)
{
    bool bRet = true;

    //-------------------------------------------------------------------------------------------------
    // Only the transmission channel number is set, and the actual hardware channel change is executed 
    // in the transmission routine within CAisModem when sending a message!
    //-------------------------------------------------------------------------------------------------
    if(uChNum != m_nChNumTx)
    {
        DEBUG_LOG("SetTxChannelNumber : %d, Freq : %d\r\n", uChNum, CAisLib::GetAisFreqByChannelNo(uChNum));

        if((bRet = CAisLib::GetAisFreqByChannelNo(uChNum)) > 0)
            m_nChNumTx = uChNum;
    }
    return bRet;
}

/**
 * @brief Get channel number for transmission
 * @return Channel number for transmission
 */
UINT16 CChannelMgr::GetTxChannelNumber(void)
{
    return m_nChNumTx;
}

/**
 * @brief Synchronize slot map for channel
 * @param nShiftSlot Number of slots to shift
 */
void CChannelMgr::SyncChannel(INT16 nShiftSlot)
{
    ShiftFrameMap(nShiftSlot);
    ShiftFrameIndex(nShiftSlot);
}

/**
 * @brief Process slot change time
 * @param bChangedFrameID
 */
void CChannelMgr::ProcessSlotChangeTime_Ch(bool bChangedFrameID)
{
    //------------------------------------------
    // slot 이 바뀌는 순간 채널별 처리해야할것들
    // CAUTION! Keep the calling sequence!!!
    //------------------------------------------
    if(!IsTxAvailableCh())
        return;

    if(bChangedFrameID)
    {
        m_pVdlTxMgr->ProcessFrameChg_TxMgr();
    }

    if(!CTestModeMgr::getInst()->IsTestModeRunning())
    {
        ProcessSlotChangeTime_Scheduler();
    }

    m_pVdlTxMgr->ProcessUnscheduledTxBuffer();

    RunProcessTxScheduler();
}

/**
 * @brief Run periodically
 */
void CChannelMgr::RunPeriodicallyCh(void)
{
    RunPeriodicallyTxScheduler();
}
