/**
 * @file    RxModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __RXMODEM_H__
#define  __RXMODEM_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AisMsg.h"
#include "GmskLib.h"

//=============================================================================
#define RX_MODEM_STATUS_PREAMBLE        (0)
#define RX_MODEM_STATUS_START           (1)
#define RX_MODEM_STATUS_PRELOAD         (2)
#define RX_MODEM_STATUS_DATA            (3)
//===========================================================================
#define RX_TRAINING_BIT                 (16)        // 24 -> 16
#define RX_START_FLAG_BIT               (8)
#define RX_OVERSAMPLE_RATE              (5)
#define RX_RAW_DATA_BUFF_SIZE           ((RX_TRAINING_BIT+RX_START_FLAG_BIT)*RX_OVERSAMPLE_RATE)*2
//===========================================================================
#define RX_RAW_FORM_BUFF_SIZE           (37)
//===========================================================================
#define RX_PREAMBLE_LEN                 ((RX_TRAINING_BIT+RX_START_FLAG_BIT)*RX_OVERSAMPLE_RATE)   // Training sequence(01..01) + Start flag(0x7E)
#define RX_TRAINING_BIT_LEN             (RX_TRAINING_BIT * RX_OVERSAMPLE_RATE)      // 16 bits * 5 = 80 samples
#define RX_START_FLAG_BIT_LEN           (RX_START_FLAG_BIT * RX_OVERSAMPLE_RATE)    // 8 bits * 5 = 40 samples
#define RX_SYNC_THRESHOLD               (0.6084)    // 0.78*0.78 Preamble max corrolation threshold
#define RX_SYNC_STABLE_CNT              (10)        // Stable max corrolation count
#define RX_DATA_OFFSET_CNT              (-7)        // Data offset count       
#define RX_MAX_ADC_ERROR_CNT            (20)        // Max adc value error count
//============================================================================
#define VITERBI_NUM_STATES              (4)
#define VITERBI_MAX_SEQ_LEN             (16)
#define INF_VALUE                       (1e10)

//============================================================================
// Viterbi States
typedef struct {
    double  path_metrics[VITERBI_NUM_STATES];                       // Path metrics for each state
    uint8_t sequences[VITERBI_NUM_STATES][VITERBI_MAX_SEQ_LEN];     // Decoded bit sequences for each state
    int     seq_len[VITERBI_NUM_STATES];                            // Current sequence length for each state
} ViterbiStates;

class CRxModem
{
public:
    CRxModem(int nRxChannelNo);
    virtual ~CRxModem(void);

    static std::shared_ptr<CRxModem> getInstRxA() {
        static std::shared_ptr<CRxModem> pInstRxA = std::make_shared<CRxModem>(AIS_CHANNEL_AIS1);
        return pInstRxA;
    }

    static std::shared_ptr<CRxModem> getInstRxB() {
        static std::shared_ptr<CRxModem> pInstRxB = std::make_shared<CRxModem>(AIS_CHANNEL_AIS2);
        return pInstRxB;
    }

protected:
    void  ClearPreambleBuff(void);
    void  ClearRxRawBuff(void);
    void  ClearRxRawFormTemp(void);
    void  ResetToRxStatusPreamble(void);

    float RunGmskFilter(float fInAdc);
    bool  RunDetectPreamble(float fInAdc);
    void  EstimateSignalGain(const float* fRcvPreamble,
                             const float* fSrcPreamble, 
                             int num, 
                             float fMaxImpulseResponse,
                             float* fH0,
                             float* fBias);
    void  CopySeqAndAddBit(const uint8_t* src_sequence, int src_length,
                            int* dst_sequence, int* dst_length, int bit);
    int8_t ViterbiMlsd(ViterbiStates* states, float rx_data, 
                        float main_signal_coeff, float isi_signal_coeff);
    void  ApplyGmskFilter(HWORD wRxAfAdcData);
    void  BufferRawData(void);
    void  NormalizeDcLevel(void);
    void  EstimateChannelParameters(void);
    void  InitializeViterbiDecoder(void);
    void  WritePacketIntoRxRawBuff(void);
    void  PutDataIntoRxRawBuff(uint8_t nRxData);
    void  ProcessRxDataCommonRun(void);
    void  DecodeCurrentSample(void);
    void  ProcessPreambleDetection(void);
    void  ProcessStartSequence(void);
    void  ProcessDataDecoding(void);

public:
    void  ProcessGmskRxData(HWORD wRxAfAdcData);
    xAisRxRawForm* GetFullPacketFromRxRawBuff(void);

protected:
    volatile int    m_nRxChannelNo;
    volatile float  m_fRxReferValue;

    volatile float  m_vpRxRawDataBuff[RX_RAW_DATA_BUFF_SIZE];
    volatile int    m_dwRxRawDataIdx;

    volatile float  m_vRxPreambleBuff[RX_PREAMBLE_LEN];
    volatile float  m_vRxRawGmskBuff[RX_GMSK_BT_0_5_FIR_N];
    volatile int    m_nGmskBuffIdx;  // GMSK 필터 순환 버퍼 인덱스

    volatile HWORD  m_wRxRunStatus;
    volatile HWORD  m_wRxBitCount;
    volatile HWORD  m_wRxShiftReg;

    volatile HWORD  m_wRxMaxBitSize;

    volatile int8_t m_nRxPrevBitD;
    volatile int8_t m_nRxCurrBitD;

    volatile HWORD  m_wCrcRegData;
    volatile UCHAR  m_bRxByteData;

    volatile xAisRxRawForm *m_vRxRawFormBuff;
    volatile int    m_nRxRawFormHead;
    volatile int    m_nRxRawFormTail;
    volatile int    m_nRxRawFormTemp;

    volatile float  m_fRxAfAdcData;

    // DC offset removal
    volatile float  m_fRxDcLevelSum;
    volatile float  m_vRxDcLevelBuff[RX_TRAINING_BIT_LEN];
    volatile HWORD  m_nRxDcLevelIdx;

    // Preamble detection
    volatile float  m_fRxNormAdc;
    volatile float  m_vRxNormBuff[RX_PREAMBLE_LEN];
    volatile int    m_dRxNormBuffIdx;
    volatile float  m_fMaxCorrVal;
    volatile int    m_dwMaxCorrIdx;
    volatile DWORD  m_dwMaxElapseCount;
    volatile int    m_dwRxDataStartIdx;
    volatile UCHAR  m_nRxOvsCnt;
    volatile float  m_fRxSyncPowSum;

    // Channel estimation
    volatile float  m_fFilteredSyncSum;
    volatile float  m_fFilteredSyncPowSum;
    volatile float  m_fSignalGain;
    volatile float  m_fSignalCoff;

    // Timing and synchronization
    volatile DWORD  m_dSampleCounter;
    volatile DWORD  m_dSlotNoCounter;

    // Viterbi decoder
    volatile ViterbiStates m_vViterbiStates;
    volatile bool m_bFirstBitSkipped;
};

#endif /*__RXMODEM_H__*/

