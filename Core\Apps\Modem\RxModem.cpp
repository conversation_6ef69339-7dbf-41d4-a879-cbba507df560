/**
 * @file    RxModem.cpp
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "AisModem.h"
#include "RxModem.h"
#include "SysLib.h"
#include "ComLib.h"
#include "AisLib.h"

// GMSK filter coefficients
static float G_vRxGmskCoefficient[RX_GMSK_BT_0_5_FIR_N] = 
{
    0.0199, 0.0755, 0.2100, 0.4384, 0.7082, 0.9209, 1.0000, 0.9209,
    0.7082, 0.4384, 0.2100, 0.0755, 0.0199
}; // BT=0.4

// GMSK filter parameters
#define GMSK_MAX_IMPULSE_RESPONSE   (1.0f)      // max(conv(G_vRxGmskCoefficient, G_vRxGmskCoefficient))
#define GMSK_ISI_IMPULSE_RESPONSE   (0.2857f)   // Symbol ISI value

// Gain offset
#define FILTERED_PREAMBLE_OFFSET    (3)         // Gain offset

//============================================================================
// preamble : 110011001100110011001100
// oversampling : 5
// impulse response : gmsk
// BT : 0.4
// GMSK preamble
static float G_vGmskPreamble[RX_PREAMBLE_LEN] =
{
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1,  1,  1,  1,  1,  1
};

// GMSK filtered preamble
static float G_vFilteredPreamble[] =
{
-0.0199, -0.0755, -0.2100, -0.4384, -0.7082, -0.9408, -1.0755, -1.1309,
-1.1466, -1.1466, -1.1110, -1.0000, -0.7308, -0.2698,  0.2698,  0.7308,
 1.0000,  1.1110,  1.1466,  1.1466,  1.1110,  1.0000,  0.7308,  0.2698,
-0.2698, -0.7308, -1.0000, -1.1110, -1.1466, -1.1466, -1.1110, -1.0000,
-0.7308, -0.2698,  0.2698,  0.7308,  1.0000,  1.1110,  1.1466,  1.1466,
 1.1110,  1.0000,  0.7308,  0.2698, -0.2698, -0.7308, -1.0000, -1.1110,
-1.1466, -1.1466, -1.1110, -1.0000, -0.7308, -0.2698,  0.2698,  0.7308,
 1.0000,  1.1110,  1.1466,  1.1466,  1.1110,  1.0000,  0.7308,  0.2698,
-0.2698, -0.7308, -1.0000, -1.1110, -1.1466, -1.1466, -1.1110, -1.0000,
-0.7308, -0.2698,  0.2698,  0.7308,  1.0000,  1.1110,  1.1466,  1.1466,
 1.1110,  1.0000,  0.7308,  0.2698, -0.2698, -0.7308, -1.0000, -1.1110,
-1.1466, -1.1466, -1.1508, -1.1510, -1.1508, -1.1466, -1.1466, -1.1508,
-1.1510, -1.1508, -1.1466, -1.1466, -1.1508, -1.1510, -1.1508, -1.1466,
-1.1466, -1.1508, -1.1510, -1.1508, -1.1466, -1.1466, -1.1508, -1.1510,
-1.1508, -1.1466, -1.1466, -1.1110, -1.0000, -0.7308, -0.2698,  0.2698,
 0.7109,  0.9245,  0.9010,  0.7082,  0.4384,  0.2100,  0.0755,  0.0199,
 0     ,  0     ,  0     ,  0
};

//============================================================================
CRxModem::CRxModem(int nRxChannelNo)
{
    // Set channel number
    m_nRxChannelNo  = nRxChannelNo;

    // Allocate ais raw form buffer
    m_vRxRawFormBuff = (xAisRxRawForm*)SysAllocMemory(sizeof(xAisRxRawForm) * RX_RAW_FORM_BUFF_SIZE);

    // Clear raw data buffer
    memset((void*)m_vpRxRawDataBuff, 0x00, sizeof(float) * RX_RAW_DATA_BUFF_SIZE);
    m_dwRxRawDataIdx = 0;

#if defined(RX_GMSK_FILTER_USE_MODE)
    // Clear GMSK filter buffer
    memset((void *)m_vRxRawGmskBuff, 0x00, sizeof(float) * RX_GMSK_BT_0_5_FIR_N);
    m_nGmskBuffIdx = 0;
#endif

    m_wRxRunStatus  = RX_MODEM_STATUS_PREAMBLE;
    m_wRxBitCount   = 0;
    m_wRxShiftReg   = 0;
    m_wRxMaxBitSize = 0;

    m_nRxPrevBitD   = 0;
    m_nRxCurrBitD   = 0;

    m_fRxAfAdcData  = 0;
    m_wCrcRegData   = 0;
    m_bRxByteData   = 0;

    // Initialize DC offset removal
    m_fRxDcLevelSum = 0;
    m_nRxDcLevelIdx = 0;
    memset((void *)m_vRxDcLevelBuff, 0x00, sizeof(float) * RX_TRAINING_BIT_LEN);

    m_fRxSyncPowSum = 0;
    m_fFilteredSyncSum = 0;
    m_fFilteredSyncPowSum = 0;
    for (int i = 0; i < RX_PREAMBLE_LEN; i++)
    {
        m_fRxSyncPowSum += (G_vGmskPreamble[i] * G_vGmskPreamble[i]);
        m_fFilteredSyncSum += G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET + i];
        m_fFilteredSyncPowSum += (G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET + i] * G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET + i]);
    }

    ClearPreambleBuff();
    ClearRxRawBuff();
} 

CRxModem::~CRxModem(void)
{
}

/**
 * @brief Clear preamble buffer
 */
void CRxModem::ClearPreambleBuff(void)
{
    memset((void*)m_vRxPreambleBuff, 0x00, sizeof(float) * RX_PREAMBLE_LEN);
    memset((void *)m_vRxNormBuff, 0x00, sizeof(float) * RX_PREAMBLE_LEN);

    m_fRxNormAdc        = 0;
    m_dRxNormBuffIdx    = 0;
    m_fMaxCorrVal       = 0;
    m_dwMaxElapseCount  = 0;
}

/**
 * @brief Clear raw data buffer
 */
void CRxModem::ClearRxRawBuff(void)
{
    memset((void *)m_vRxRawFormBuff, 0x00, sizeof(xAisRxRawForm)*RX_RAW_FORM_BUFF_SIZE);
    m_nRxRawFormHead = 0;
    m_nRxRawFormTail = 0;
    m_nRxRawFormTemp = 0;
}

void CRxModem::ClearRxRawFormTemp(void)
{
    m_nRxRawFormTemp = 0;
}

/**
 * @brief Reset to preamble status
 */
void CRxModem::ResetToRxStatusPreamble(void)
{
    m_wRxRunStatus = RX_MODEM_STATUS_PREAMBLE;
    m_wRxShiftReg  = 0;
    m_fRxReferValue= 0;
}

#if defined(RX_GMSK_FILTER_USE_MODE)
/**
 * @brief Run GMSK filter
 * @param fInAdc Input ADC value
 * @return float Output ADC value
 */
float CRxModem::RunGmskFilter(float fInAdc)
{
    float fOutAdc = 0;

    m_vRxRawGmskBuff[m_nGmskBuffIdx] = fInAdc;
    if (++m_nGmskBuffIdx >= RX_GMSK_BT_0_5_FIR_N)
        m_nGmskBuffIdx = 0;

    // Convolution with GMSK filter coefficients using circular buffer
    int buffIdx = m_nGmskBuffIdx;
    for (int i = 0; i < RX_GMSK_BT_0_5_FIR_N; i++)
    {
        fOutAdc += (m_vRxRawGmskBuff[buffIdx] * G_vRxGmskCoefficient[i]);

        if (++buffIdx >= RX_GMSK_BT_0_5_FIR_N)
            buffIdx = 0;
    }

#if defined(__DEBUG_RXMODEM_FILTER__) && defined(DBG_SET_TEST_CHANNEL)
    if(m_nRxChannelNo == DBG_SET_TEST_CHANNEL)
        cDac::getInst()->SetDAC1Data(fOutAdc);
#endif

    return fOutAdc;
}
#endif

/**
 * @brief Detect preamble
 * @param fInAdc Input ADC value
 * @return bool True if preamble is detected
 */
bool CRxModem::RunDetectPreamble(float fInAdc)
{
	float fCrossSync = 0;
	float fSyncCorrVal  = 0;
	int   nBuffIdx   = 0;
	bool bDetected = false;

    // Pre calculate power value
    m_fRxNormAdc -= (m_vRxNormBuff[m_dRxNormBuffIdx] * m_vRxNormBuff[m_dRxNormBuffIdx]);
    m_fRxNormAdc += (fInAdc * fInAdc);
    m_vRxNormBuff[m_dRxNormBuffIdx] = fInAdc;

    if (++m_dRxNormBuffIdx >= RX_PREAMBLE_LEN)
        m_dRxNormBuffIdx = 0;

    // convolution with preamble
    nBuffIdx = m_dRxNormBuffIdx;
    for (int i = 0; i < RX_PREAMBLE_LEN; i++)
    {
        fCrossSync += (m_vRxNormBuff[nBuffIdx] * G_vGmskPreamble[i]);

        if (++nBuffIdx >= RX_PREAMBLE_LEN)
            nBuffIdx = 0;
    }

    // calculate correlation value
    if (m_fRxNormAdc != 0 && m_fRxSyncPowSum != 0)
        fSyncCorrVal = (fCrossSync * fCrossSync) / (m_fRxNormAdc * m_fRxSyncPowSum);
    else
        fSyncCorrVal = 0;

    // find correlation peak point
    if ((fSyncCorrVal > RX_SYNC_THRESHOLD) && (fSyncCorrVal > m_fMaxCorrVal))
    {
        m_fMaxCorrVal = fSyncCorrVal;
        m_dwMaxCorrIdx = m_dwRxRawDataIdx;
        m_dwMaxElapseCount = 0;

        // Set data start index
        if (m_dwRxRawDataIdx + RX_DATA_OFFSET_CNT >= 0)
            m_dwRxDataStartIdx = m_dwRxRawDataIdx + RX_DATA_OFFSET_CNT;
        else
            m_dwRxDataStartIdx = RX_RAW_DATA_BUFF_SIZE + m_dwRxRawDataIdx + RX_DATA_OFFSET_CNT;

        // Calculate DC reference value
        if ((m_nRxDcLevelIdx - RX_START_FLAG_BIT_LEN) >= 0)
            m_fRxReferValue = m_vRxDcLevelBuff[m_nRxDcLevelIdx - RX_START_FLAG_BIT_LEN];
        else
            m_fRxReferValue = m_vRxDcLevelBuff[RX_TRAINING_BIT_LEN + m_nRxDcLevelIdx - RX_START_FLAG_BIT_LEN];
    }
    else if (m_fMaxCorrVal > RX_SYNC_THRESHOLD)
    {
        m_dwMaxElapseCount++;

        // Confirm peak
        if (m_dwMaxElapseCount >= RX_SYNC_STABLE_CNT)
        {
            bDetected = true;
            m_wRxRunStatus = RX_MODEM_STATUS_START;
            m_nRxOvsCnt = RX_OVERSAMPLE_RATE;
            m_wRxBitCount = 0;
        }
    }
    else
    {
        m_fMaxCorrVal = 0;
        m_dwMaxElapseCount = 0;
    }

    return bDetected;
}

/**
 * @brief Estimate signal gain
 * @param fRcvPreamble Received preamble
 * @param fSrcPreamble Source preamble
 * @param nNum Number of samples
 * @param fMaxImpulseResponse Maximum impulse response
 * @param fH0 Output H0 value
 * @param fBias Output bias value
 */
void CRxModem::EstimateSignalGain(
                                const float* fRcvPreamble,
                                const float* fSrcPreamble,
                                int nNum,
                                float  fMaxImpulseResponse,
                                float* fH0,
                                float* fBias
                                )
{
    // 채널 임펄스 응답의 중심 계수 : 현재 전송되는 심볼이 수신 신호에 미치는 영향의 크기
    // ISI(Inter - Symbol Interference) 모델링 : h1과 함께 사용되어 이전 / 이후 심볼이 현재 심볼에 미치는 간섭을 모델링
    // Viterbi MLSD 알고리즘의 핵심 파라미터 : 채널 특성을 반영하여 최적의 비트 시퀀스를 복원하는데 사용

    float fRcvPowSum = 0.0f, fRcvSum = 0.0f;
    for (int i = 0; i < nNum; i++) {
        fRcvPowSum += fSrcPreamble[i] * fRcvPreamble[i];
        fRcvSum += fRcvPreamble[i];
    }

    float fDetVal = m_fFilteredSyncPowSum * nNum - m_fFilteredSyncSum * m_fFilteredSyncSum;
    float fH0Val = 0.0f, fBiasVal = 0.0f;

    if (fabsf(fDetVal) > 1e-10f) {
        float h0_scale = ((nNum * fRcvPowSum) - (m_fFilteredSyncSum * fRcvSum)) / fDetVal;
        fBiasVal = ((m_fFilteredSyncPowSum * fRcvSum) - (m_fFilteredSyncSum * fRcvPowSum)) / fDetVal;
        fH0Val = h0_scale * fMaxImpulseResponse;
    }
    else {
        fBiasVal = (fRcvSum / nNum) - (m_fFilteredSyncSum / nNum);

        float fSumDc = 0.0f;
        for (int i = 0; i < nNum; i++) {
            float r_dc = fRcvPreamble[i] - fBiasVal;
            fSumDc += r_dc * fSrcPreamble[i];
        }

        float fH0Scale = fSumDc / m_fFilteredSyncPowSum;
        fH0Val = fH0Scale * fMaxImpulseResponse;
    }

    *fH0 = fabsf(fH0Val);
    *fBias = fabsf(fBiasVal);
}

/**
 * @brief Copy sequence and add bit
 * @param src_sequence Source sequence
 * @param src_length Source sequence length
 * @param dst_sequence Destination sequence
 * @param dst_length Destination sequence length
 * @param bit Bit to add
 */
void CRxModem::CopySeqAndAddBit(const uint8_t* src_sequence, int src_length,
                                int* dst_sequence, int* dst_length, int bit)
{
    *dst_length = 0;

    // 기존 시퀀스 복사
    for (int i = 0; i < src_length && i < VITERBI_MAX_SEQ_LEN - 1; i++) {
        dst_sequence[i] = src_sequence[i];
        (*dst_length)++;
    }

    // 새 비트 추가
    if (*dst_length < VITERBI_MAX_SEQ_LEN) {
        dst_sequence[*dst_length] = bit;
        (*dst_length)++;
    }
}

/**
 * @brief Viterbi MLSD algorithm
 * @param states Viterbi states
 * @param rx_data Received data
 * @param main_signal_coeff Main signal coefficient
 * @param isi_signal_coeff ISI signal coefficient
 * @return int8_t Decoded bit
 */
int8_t CRxModem::ViterbiMlsd(ViterbiStates* states, float rx_data,
    float main_signal_coeff, float isi_signal_coeff) {
    
    // ISI를 고려한 8가지 예상 신호 값 계산
    double h_values[8] = {
        -isi_signal_coeff - main_signal_coeff - isi_signal_coeff,  // [-1,-1,-1]
        -isi_signal_coeff - main_signal_coeff + isi_signal_coeff,  // [-1,-1,+1]
        -isi_signal_coeff + main_signal_coeff - isi_signal_coeff,  // [-1,+1,-1]
        -isi_signal_coeff + main_signal_coeff + isi_signal_coeff,  // [-1,+1,+1]
         isi_signal_coeff - main_signal_coeff - isi_signal_coeff,  // [+1,-1,-1]
         isi_signal_coeff - main_signal_coeff + isi_signal_coeff,  // [+1,-1,+1]
         isi_signal_coeff + main_signal_coeff - isi_signal_coeff,  // [+1,+1,-1]
         isi_signal_coeff + main_signal_coeff + isi_signal_coeff   // [+1,+1,+1]
    };

    // 상태 전이 테이블: [현재상태][입력비트] = {다음상태, h_values인덱스}
    struct { int next_state; int h_idx; } transitions[4][2] = {
        {{0, 0}, {1, 1}},  // 상태 00: 비트0→상태00, 비트1→상태01
        {{2, 2}, {3, 3}},  // 상태 01: 비트0→상태10, 비트1→상태11
        {{0, 4}, {1, 5}},  // 상태 10: 비트0→상태00, 비트1→상태01
        {{2, 6}, {3, 7}}   // 상태 11: 비트0→상태10, 비트1→상태11
    };

    double new_path_metrics[VITERBI_NUM_STATES];
    int new_sequences[VITERBI_NUM_STATES][VITERBI_MAX_SEQ_LEN];
    int new_seq_len[VITERBI_NUM_STATES];

    // 각 상태별로 최적 경로 선택
    for (int next_state = 0; next_state < VITERBI_NUM_STATES; next_state++) {
        double best_metric = INF_VALUE;
        int best_prev_state = 0;
        int best_bit = 0;

        // 현재 상태로 올 수 있는 모든 경로 검사
        for (int prev_state = 0; prev_state < VITERBI_NUM_STATES; prev_state++) {
            for (int bit = 0; bit < 2; bit++) {
                if (transitions[prev_state][bit].next_state == next_state) {
                    double branch_metric = (rx_data - h_values[transitions[prev_state][bit].h_idx]);
                    branch_metric *= branch_metric;
                    double path_metric = states->path_metrics[prev_state] + branch_metric;
                    
                    if (path_metric < best_metric) {
                        best_metric = path_metric;
                        best_prev_state = prev_state;
                        best_bit = bit;
                    }
                }
            }
        }

        // 최적 경로 저장
        new_path_metrics[next_state] = best_metric;
        CopySeqAndAddBit(states->sequences[best_prev_state], states->seq_len[best_prev_state],
            new_sequences[next_state], &new_seq_len[next_state], best_bit);
    }

    // 상태 업데이트
    for (int i = 0; i < VITERBI_NUM_STATES; i++) {
        states->path_metrics[i] = new_path_metrics[i];
        states->seq_len[i] = new_seq_len[i];
        for (int j = 0; j < new_seq_len[i]; j++) {
            states->sequences[i][j] = new_sequences[i][j];
        }
    }

    // Early decision
    if (states->seq_len[0] > 0 && states->seq_len[1] > 0 &&
        states->seq_len[2] > 0 && states->seq_len[3] > 0) {
        
        uint8_t decided_bit = states->sequences[0][0];
        if (states->sequences[1][0] == decided_bit &&
            states->sequences[2][0] == decided_bit &&
            states->sequences[3][0] == decided_bit) {

            // Shift all sequences left by 1 position
            for (int s = 0; s < VITERBI_NUM_STATES; s++) {
                if (--states->seq_len[s] > 0) {
                    memmove(states->sequences[s], states->sequences[s] + 1, states->seq_len[s]);
                }
            }
            return decided_bit;
        }
    }

    return -1;
}

/**
 * @brief Apply GMSK filter
 * @param wRxAfAdcData Input ADC value
 */
void CRxModem::ApplyGmskFilter(HWORD wRxAfAdcData)
{
#if defined(RX_GMSK_FILTER_USE_MODE)
    m_fRxAfAdcData = RunGmskFilter(wRxAfAdcData);
#else
    m_fRxAfAdcData = wRxAfAdcData;
#endif
}

/**
 * @brief Buffer raw data
 */
void CRxModem::BufferRawData(void)
{
    if (++m_dwRxRawDataIdx >= RX_RAW_DATA_BUFF_SIZE)
        m_dwRxRawDataIdx = 0;
    
    m_vpRxRawDataBuff[m_dwRxRawDataIdx] = m_fRxAfAdcData;
}

/**
 * @brief Normalize DC level
 */
void CRxModem::NormalizeDcLevel(void)
{
    // Update moving average filter
    int prevIdx = (m_dwRxRawDataIdx >= RX_TRAINING_BIT_LEN) ? 
        m_dwRxRawDataIdx - RX_TRAINING_BIT_LEN : 
        RX_RAW_DATA_BUFF_SIZE - (RX_TRAINING_BIT_LEN - m_dwRxRawDataIdx);
    
    m_fRxDcLevelSum -= m_vpRxRawDataBuff[prevIdx];
    m_fRxDcLevelSum += m_vpRxRawDataBuff[m_dwRxRawDataIdx];
    
    if (++m_nRxDcLevelIdx >= RX_TRAINING_BIT_LEN)
        m_nRxDcLevelIdx = 0;
    
    m_vRxDcLevelBuff[m_nRxDcLevelIdx] = m_fRxDcLevelSum / RX_TRAINING_BIT_LEN;
    
    // Remove DC offset
    m_fRxAfAdcData -= m_vRxDcLevelBuff[m_nRxDcLevelIdx];
}

/**
 * @brief Estimate channel parameters
 */
void CRxModem::EstimateChannelParameters(void)
{
    float fSignalGain = 0.0f;
    float fBias = 0.0f;
    int nPreambleIdx = (m_dwMaxCorrIdx > 0) ? m_dwMaxCorrIdx - 1 : RX_RAW_DATA_BUFF_SIZE - 1;
    
    // Copy preamble data into temporary buffer
    for (int idx = 0; idx < RX_PREAMBLE_LEN; idx++)
    {
        m_vRxPreambleBuff[RX_PREAMBLE_LEN - idx - 1] = 
            m_vpRxRawDataBuff[nPreambleIdx] - m_fRxReferValue;
        
        if (--nPreambleIdx < 0)
            nPreambleIdx = RX_RAW_DATA_BUFF_SIZE - 1;
    }
    
    // Estimate signal gain and bias
    EstimateSignalGain(
        (float*)m_vRxPreambleBuff,
        &G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET],
        RX_PREAMBLE_LEN,
        GMSK_MAX_IMPULSE_RESPONSE,
        &fSignalGain,
        &fBias
    );
    
    // Update signal gain and bias value
    m_fSignalGain = GMSK_MAX_IMPULSE_RESPONSE * fSignalGain;
    m_fSignalCoff = GMSK_ISI_IMPULSE_RESPONSE * fSignalGain;
}

/**
 * @brief Initialize Viterbi decoder
 */
void CRxModem::InitializeViterbiDecoder(void)
{
    memset((void*)&m_vViterbiStates, 0x00, sizeof(ViterbiStates));
    m_bFirstBitSkipped = false;
}

void  CRxModem::WritePacketIntoRxRawBuff(void)
{
    if(m_nRxRawFormTemp == 0 || m_wRxBitCount < 40)
        return;
    m_vRxRawFormBuff[m_nRxRawFormHead].wMsgFrameNO    = CAisModem::getInst()->GetCurrentFrameNo();
    m_vRxRawFormBuff[m_nRxRawFormHead].dSlotNoCounter = m_dSlotNoCounter;
    m_vRxRawFormBuff[m_nRxRawFormHead].dSampleCounter = m_dSampleCounter;
    m_vRxRawFormBuff[m_nRxRawFormHead].wRxByteSize    = m_nRxRawFormTemp -  2; // - FCS (2-bytes)
    m_vRxRawFormBuff[m_nRxRawFormHead].wRxBitsSize    = m_wRxBitCount    - 16; // - FCS (16-bits)

    ++m_nRxRawFormHead;
    if(m_nRxRawFormHead >= RX_RAW_FORM_BUFF_SIZE)
        m_nRxRawFormHead  = 0;
}

/**
 * @brief Put data into raw buffer
 * @param bRxData Data to put
 */
void  CRxModem::PutDataIntoRxRawBuff(uint8_t nRxData)
{
    if(m_nRxRawFormTemp < (AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX - 2))
        m_vRxRawFormBuff[m_nRxRawFormHead].vRxRawData[m_nRxRawFormTemp++] = nRxData;
}

/**
 * @brief Process received data
 */
void CRxModem::ProcessRxDataCommonRun(void)
{
    m_wRxShiftReg <<= 1;

    if (m_nRxCurrBitD == m_nRxPrevBitD)
        m_wRxShiftReg |= 0x0001;
    else
        m_wRxShiftReg &= 0xfffe;

    m_nRxPrevBitD = m_nRxCurrBitD;

    switch (m_wRxRunStatus)
    {
    case RX_MODEM_STATUS_PRELOAD:
        if(++m_wRxBitCount == 8) {
            m_wRxBitCount = 0;
            m_wCrcRegData = 0xffff;
            m_wRxRunStatus= RX_MODEM_STATUS_DATA;
            ClearRxRawFormTemp();

            int8_t nMsgID = GetReverseBitValue((m_wRxShiftReg << 2) & 0xff);
            if(nMsgID < 0 || nMsgID > 27) {
                ResetToRxStatusPreamble();
            }
            else {
                m_wRxMaxBitSize = CAisLib::GetAisRxMsgMaxBitSizeByMsgID(nMsgID);
            }
       }
       break;

    case RX_MODEM_STATUS_DATA:
        if((m_wRxShiftReg & 0x3f00) != 0x3e00)        // It's not a stuffing bit
        {
            ++m_wRxBitCount;
            if(m_wRxBitCount >= m_wRxMaxBitSize) {
                ResetToRxStatusPreamble();
                return;
            }

            uint8_t nNewBitData = (m_wRxShiftReg >> 8) & 0x0001;
            m_bRxByteData = (m_bRxByteData >> 1) | ((m_wRxShiftReg >> 1) & 0x0080);
            if(!(m_wRxBitCount & 0x07))
                PutDataIntoRxRawBuff(m_bRxByteData);

            if((m_wCrcRegData ^ nNewBitData) & 0x0001)          // Pass new bit through CRC calculation
                m_wCrcRegData = (m_wCrcRegData >> 1) ^ 0x8408;  // Xor with the CRC polynomial (X^16 + X^12 + X^5 + 1)
            else
                m_wCrcRegData >>= 1;
        }

        if((m_wRxShiftReg & 0x00ff) == 0x007e) {
            if(m_wCrcRegData == 0xf0b8) {    // This should give a result of 0xF0B8
                WritePacketIntoRxRawBuff();
                ResetToRxStatusPreamble();
            }
            else {
                m_dSampleCounter = CAisModem::getInst()->GetSampleCounter();
                m_dSlotNoCounter = CAisModem::getInst()->GetSlotNoCounter();

                m_wRxBitCount = 0;
                ResetToRxStatusPreamble();
            }
        }
        break;

    default:
        break;
    }
}

/**
 * @brief Decode current sample
 */
void CRxModem::DecodeCurrentSample(void)
{
    // Get raw data excluding DC bias
    float fRxAfAdcRawX = m_vpRxRawDataBuff[m_dwRxDataStartIdx] - m_fRxReferValue;

    // Decode current sample
    m_nRxCurrBitD = ViterbiMlsd(
        (ViterbiStates*)&m_vViterbiStates, 
        fRxAfAdcRawX, 
        m_fSignalGain, 
        m_fSignalCoff
    );
    
    if (m_nRxCurrBitD >= 0)
    {
        // If it's the first bit, skip it
        if (!m_bFirstBitSkipped)
        {
            m_bFirstBitSkipped = true;
            m_nRxPrevBitD = m_nRxCurrBitD;
        }
        else
        {
            ProcessRxDataCommonRun();
        }
    }
}

/**
 * @brief Process preamble detection
 */
void CRxModem::ProcessPreambleDetection(void)
{
    if (RunDetectPreamble(m_fRxAfAdcData))
        ClearPreambleBuff();
}

/**
 * @brief Process start sequence
 */
void CRxModem::ProcessStartSequence(void)
{
    EstimateChannelParameters();
    InitializeViterbiDecoder();
    m_wRxRunStatus = RX_MODEM_STATUS_PRELOAD;
}

/**
 * @brief Process data decoding
 */
void CRxModem::ProcessDataDecoding(void)
{
    do {
        if (++m_nRxOvsCnt >= RX_OVERSAMPLE_RATE)
        {
            DecodeCurrentSample();
            m_nRxOvsCnt = 0;
        }
        
        if (++m_dwRxDataStartIdx >= RX_RAW_DATA_BUFF_SIZE)
            m_dwRxDataStartIdx = 0;
    } while (m_dwRxDataStartIdx != m_dwRxRawDataIdx);
}

/**
 * @brief Process GMSK RX data
 * @param wRxAfAdcData Input ADC value
 * @return int 0
 */
void CRxModem::ProcessGmskRxData(HWORD wRxAfAdcData)
{
    // Apply GMSK filter if enabled
    ApplyGmskFilter(wRxAfAdcData);

    // Buffering rx raw data
    BufferRawData();

    // Normalize DC level
    NormalizeDcLevel();

    // Process based on current receiver status
    switch (m_wRxRunStatus)
    {
        case RX_MODEM_STATUS_PREAMBLE:
            ProcessPreambleDetection();
            break;
            
        case RX_MODEM_STATUS_START:
            ProcessStartSequence();
            break;
            
        default:
            ProcessDataDecoding();
            break;
    }
}

/**
 * @brief Get full packet from raw buffer
 * @return xAisRxRawForm* Pointer to the full packet
 */
xAisRxRawForm *CRxModem::GetFullPacketFromRxRawBuff(void)
{
    xAisRxRawForm *pAisRxRawForm;

    if(m_nRxRawFormHead == m_nRxRawFormTail)
        return(NULL);

    pAisRxRawForm = (xAisRxRawForm*)&m_vRxRawFormBuff[m_nRxRawFormTail];

    ++m_nRxRawFormTail;
    if(m_nRxRawFormTail >= RX_RAW_FORM_BUFF_SIZE)
        m_nRxRawFormTail  = 0;

    return(pAisRxRawForm);
}
