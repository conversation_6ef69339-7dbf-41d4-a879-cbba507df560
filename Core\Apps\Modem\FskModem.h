/**
 * @file    FskModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __FSKMODEM_H__
#define  __FSKMODEM_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"

//=============================================================================
#define  FSK_MDM_MARK_FREQUENCY              1300
#define  FSK_MDM_SPACE_FREQUENCY             2100
#define  FSK_MDM_BAUD_RATE                   1200
#define  FSK_MDM_SAMPLES_PER_BIT             8
#define  FSK_MDM_SAMPLING_FREQUENCY          (FSK_MDM_BAUD_RATE * FSK_MDM_SAMPLES_PER_BIT)
//=============================================================================
#define  FSK_MDM_ADC_CENTER_VALUE            (1100 * ADC_RES_MAX_VALUE / 3300)  // 1.1V
//=============================================================================
#define  FSK_MDM_MUL_FACTOR                  2048
//=============================================================================
#define  FSK_SW_PLL_FULL_VALUE               (2400)
#define  FSK_SW_PLL_HALF_VALUE               (FSK_SW_PLL_FULL_VALUE / 2)
#define  FSK_SW_PLL_INCR_VALUE               (FSK_SW_PLL_FULL_VALUE / FSK_MDM_SAMPLES_PER_BIT)
#define  FSK_SW_PLL_STEP_VALUE               (FSK_SW_PLL_INCR_VALUE / 3)
//=============================================================================
#define  FSK_MDM_RX_STATUS_NONE              0
#define  FSK_MDM_RX_STATUS_RUN               1
//=============================================================================
#define  FSK_MDM_RX_BUFF_SIZE                (256*10)     // DSC_MAX_BUFF_SIZE
//=============================================================================
#define  FSK_MDM_RX_BIT_DATA_NULL            -1
//=============================================================================
#define  FSK_MDM_RX_RAW_BUFF_SIZE            (64)         // 12~16, MAX=16
//=============================================================================

class CFskModem
{
public:
    CFskModem(void);
    virtual ~CFskModem(void);

    static std::shared_ptr<CFskModem> getInst() {
        static std::shared_ptr<CFskModem> pInst = std::make_shared<CFskModem>();
        return pInst;
    }

public:
    int     GetFskRxStatus(void);
    int     GetFskRawData(void);
    int     GetFskBitData(void);
    void    PutFskBitData(int nBitData);
    void    ClearBuffData(void);
    void    ResetFskModem(void);
    void    RunTimerIsrHandler(int nDscAdcData);
    int     CalcFskFirFilter(int *pCoff);

protected:
    int      m_nRunSequenceCntr;
    int      *m_vMarkSinData;
    int      *m_vMarkCosData;
    int      *m_vSpaceSinData;
    int      *m_vSpaceCosData;
    int      *m_vSamplingData;
    int      m_nSumOfMarkI;
    int      m_nSumOfMarkQ;
    int      m_nSumOfSpaceI;
    int      m_nSumOfSpaceQ;
    int      m_nFskBitDataX;
    int      m_nFskBitDataY;
    int      m_nFskBitDataZ;
    int      m_nFskBitDataP;        // Previous data
    int      m_nFskBitDataC;        // Current  data
    volatile DWORD  m_dBitCntForPrev;
    volatile DWORD  m_dBitCntForSync;
    volatile DWORD  m_dSwRxPllValue;
    volatile HWORD  m_wRxRunBitData;
    volatile DWORD  m_dRxRunStatusX;
    volatile int    m_nRxBuffHead;
    volatile int    m_nRxBuffTail;
    volatile UCHAR *m_vRxBuffData;
    HWORD    m_vRxRawData[FSK_MDM_RX_RAW_BUFF_SIZE];
    HWORD    m_wRxRawPntX;
    HWORD    m_wRxRawAvrX;
    DWORD    m_dRxRawSumX;
};

#endif /*__FSKMODEM_H__*/
