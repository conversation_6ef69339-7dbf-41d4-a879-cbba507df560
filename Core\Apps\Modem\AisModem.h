/**
 * @file    AisModem.h
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 *
 * @copyright Copyright (c) 2025
 */

#ifndef  __AISMODEM_H__
#define  __AISMODEM_H__

#include <memory>
#include "DataType.h"
#include "AisMsg.h"
#include "GmskLib.h"
#include "RxModem.h"

class CAisModem
{
public:
    CAisModem(std::shared_ptr<CRxModem> RxModemA, std::shared_ptr<CRxModem> RxModemB);
    virtual ~CAisModem(void);

    static std::shared_ptr<CAisModem> getInst() {
        static std::shared_ptr<CAisModem> pInst = std::make_shared<CAisModem>(CRxModem::getInstRxA(), CRxModem::getInstRxB());
        return pInst;
    }

public:
    void  InitModemSetup(WORD wDcOffset, WORD wDcShift);

    void  ProcessTxRxAllInINTR(HWORD wRxAfAdcDataA, HWORD wRxAfAdcDataB);
    void  ProcessTxInINTR(void);

    void  AddTotalSlotShift(int nCurShiftSlot);
    void  RecalcNewFrameID(int nOldSlotId);
    BOOL  RunSyncToOtherSt(UINT uSyncSrcMMSI, xAisRxRawForm *pAisRxRawForm, UINT16 wRcvSubMsg, int nDistDelaySampCntr);;

    void  SetAisTxFrequency(int nFrequency);
    int   GetAisTxFrequency();

    HWORD IncCurrentFrameNo(void);
    HWORD DecCurrentFrameNo(void);

    DWORD GetSampleCounter(void)    {return (m_nSampleCounter);}
    DWORD GetSlotNoCounter(void)    {return (m_nSlotNoCounter);}
    HWORD GetCurrentFrameNo(void)   {return (m_nCurrentFrameNo);}

    SHORT GetTotalSlotShift(void)   {return (m_nTotalSlotShift);}
    SHORT GetUtcSyncSec(void)       {return (m_nUtcSyncSec);}

    void  SetInfinteTxMode(BOOL bOn);
    void  ResetAisMsgTx();

protected:
    volatile int    m_nSampleCounter;
    volatile int    m_nSlotNoCounter;
    volatile INT16  m_nCurrentFrameNo;

    volatile SHORT  m_nTotalSlotShift;
    volatile SHORT  m_nUtcSyncSec;

    HWORD           m_vTxGmskCnterDAC[2];

    volatile int    m_nUtcIndirectMode;
    volatile int    m_nUtcIndirectSlot;
    volatile int    m_nUtcIndirectSamp;
    volatile DWORD  m_dUtcIndirectTick;

    volatile DWORD  m_dUtcDirectPrevPin;
    volatile DWORD  m_dUtcDirectCurrPin;

    volatile BOOL   m_bRunTxBuffReset;
    volatile BOOL   m_bInfiniteTxMode;

    xAisTxGmskDATA *m_pAisTxGmskDATA;

    std::shared_ptr<CRxModem> m_pRxModemA;
    std::shared_ptr<CRxModem> m_pRxModemB;
};

#endif    /*__AISMODEM_H__*/
